import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// GitHub profile type
interface GitHubProfile {
  id: number;
  login: string;
  name?: string;
  email?: string;
  avatar_url?: string;
}

// Callback parameters type
interface SignInParams {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
  account?: {
    provider: string;
    type: string;
  };
  profile?: any;
}

export const auth = betterAuth({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  trustedOrigins: ["http://localhost:3000", "https://onlyrules.codes"],
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: false,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      scope: ["read:user", "user:email"],
      profile(profile: GitHubProfile) {
        return {
          id: profile.id.toString(),
          name: profile.name || profile.login,
          email: profile.email,
          image: profile.avatar_url,
        };
      },
    },
  },
  callbacks: {
    async signIn({ user, account, profile }: SignInParams) {
      // Handle users without email
      if (!user.email && account?.provider === "github") {
        console.log("GitHub user without email:", { user, profile });
        
        // Try to get email from profile if available
        if (profile && (profile as any).email) {
          user.email = (profile as any).email;
        } else {
          // Create a placeholder email as fallback
          const sanitizedName = user.name?.toLowerCase().replace(/[^a-z0-9]/g, '.') || 'user';
          user.email = `${sanitizedName}.${Date.now()}@github.user`;
          console.log("Created placeholder email:", user.email);
        }
      }
      return true;
    },
  },
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session['user'];