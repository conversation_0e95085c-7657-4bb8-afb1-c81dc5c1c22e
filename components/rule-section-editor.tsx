"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  MoreVertical,
  Trash2,
  Copy,
  GripVertical,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CodeEditor } from "@/components/ui/code-editor";
import { RuleSection } from "@/lib/store";
import { validateSection, getSectionSummary } from "@/lib/rule-sections";
import { Box, Flex, Text, Grid } from "@radix-ui/themes";

interface RuleSectionEditorProps {
  section: RuleSection;
  index: number;
  totalSections: number;
  isExpanded: boolean;
  onUpdate: (section: RuleSection) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onToggleExpanded: () => void;
}

export function RuleSectionEditor({
  section,
  index,
  totalSections,
  isExpanded,
  onUpdate,
  onDelete,
  onDuplicate,
  onMoveUp,
  onMoveDown,
  onToggleExpanded,
}: RuleSectionEditorProps) {
  const [errors, setErrors] = useState<string[]>([]);

  const handleFieldChange = (field: keyof RuleSection, value: string) => {
    const updatedSection = { ...section, [field]: value };
    onUpdate(updatedSection);
    
    // Validate on change
    const validationErrors = validateSection(updatedSection);
    setErrors(validationErrors);
  };

  const sectionSummary = getSectionSummary(section);
  const hasErrors = errors.length > 0;

  return (
    <Card className={hasErrors ? 'error-border' : ''} style={{ transition: 'all 0.2s ease' }}>
      <CardHeader style={{ paddingBottom: 'var(--space-3)' }}>
        <Flex justify="between" align="center">
          <Flex align="center" gap="3" style={{ flex: 1 }}>
            <Flex align="center" gap="1">
              <GripVertical size={16} style={{ color: 'var(--gray-9)', cursor: 'grab' }} />
              <Text size="2" weight="medium" style={{ color: 'var(--gray-9)' }}>
                {index + 1}
              </Text>
            </Flex>

            <Box style={{ flex: 1 }}>
              <CardTitle style={{ fontSize: 'var(--font-size-3)' }}>
                {section.title || `Section ${index + 1}`}
              </CardTitle>
              {!isExpanded && (
                <Text size="2" style={{ color: 'var(--gray-9)', marginTop: 'var(--space-1)' }} truncate>
                  {sectionSummary}
                </Text>
              )}
            </Box>
          </Flex>

          <Flex align="center" gap="2">
            {/* Move buttons */}
            <Flex align="center">
              <Button
                variant="ghost"
                size="1"
                onClick={onMoveUp}
                disabled={index === 0}
                style={{ width: '32px', height: '32px', padding: 0 }}
              >
                <ChevronUp size={16} />
              </Button>
              <Button
                variant="ghost"
                size="1"
                onClick={onMoveDown}
                disabled={index === totalSections - 1}
                style={{ width: '32px', height: '32px', padding: 0 }}
              >
                <ChevronDown size={16} />
              </Button>
            </Flex>

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button variant="ghost" size="1" style={{ width: '32px', height: '32px', padding: 0 }}>
                  <MoreVertical size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onDuplicate}>
                  <Copy size={16} style={{ marginRight: 'var(--space-2)' }} />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={onDelete}
                  disabled={totalSections === 1}
                  className="error-text"
                >
                  <Trash2 size={16} style={{ marginRight: 'var(--space-2)' }} />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Expand/Collapse button */}
            <Button
              variant="ghost"
              size="1"
              onClick={onToggleExpanded}
              style={{ width: '32px', height: '32px', padding: 0 }}
            >
              {isExpanded ? (
                <ChevronUp size={16} />
              ) : (
                <ChevronDown size={16} />
              )}
            </Button>
          </Flex>
        </Flex>

        {hasErrors && (
          <Box style={{ marginTop: 'var(--space-2)' }}>
            {errors.map((error, i) => (
              <Text key={i} size="2" className="error-text" style={{ display: 'block' }}>
                • {error}
              </Text>
            ))}
          </Box>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-4)' }}>
          {/* Section Title */}
          <Box>
            <Label htmlFor={`section-title-${section.id}`}>Section Title</Label>
            <Input
              id={`section-title-${section.id}`}
              value={section.title}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              placeholder="Enter section title..."
            />
          </Box>

          {/* Optional Metadata */}
          <Grid columns={{ initial: '1', md: '3' }} gap="4">
            <Box>
              <Label htmlFor={`section-name-${section.id}`}>Name (Optional)</Label>
              <Input
                id={`section-name-${section.id}`}
                value={section.name || ''}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder="e.g., global, stylesheet"
              />
            </Box>

            <Box>
              <Label htmlFor={`section-globs-${section.id}`}>File Patterns (Optional)</Label>
              <Input
                id={`section-globs-${section.id}`}
                value={section.globs || ''}
                onChange={(e) => handleFieldChange('globs', e.target.value)}
                placeholder="e.g., **.css, *.js"
              />
            </Box>

            <Box>
              <Label htmlFor={`section-description-${section.id}`}>Description (Optional)</Label>
              <Input
                id={`section-description-${section.id}`}
                value={section.description || ''}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Brief description"
              />
            </Box>
          </Grid>

          {/* Section Content */}
          <Box>
            <Label htmlFor={`section-content-${section.id}`}>Content</Label>
            <CodeEditor
              value={section.content}
              onChange={(value) => handleFieldChange('content', value)}
              placeholder="Enter section content (markdown supported)..."
            />
          </Box>
        </CardContent>
      )}
    </Card>
  );
}
