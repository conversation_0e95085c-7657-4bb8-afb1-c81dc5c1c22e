import * as React from 'react';
import { Skeleton as RadixSkeleton } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Skeleton = React.forwardRef<
  React.ElementRef<typeof RadixSkeleton>,
  React.ComponentPropsWithoutRef<typeof RadixSkeleton>
>(({ className, ...props }, ref) => (
  <RadixSkeleton
    ref={ref}
    className={cn('', className)}
    {...props}
  />
));
Skeleton.displayName = 'Skeleton';

export { Skeleton };
