'use client';

import * as React from 'react';
import { Checkbox as RadixCheckbox } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Checkbox = React.forwardRef<
  React.ElementRef<typeof RadixCheckbox>,
  React.ComponentPropsWithoutRef<typeof RadixCheckbox>
>(({ className, ...props }, ref) => (
  <RadixCheckbox
    ref={ref}
    className={cn('', className)}
    {...props}
  />
));
Checkbox.displayName = 'Checkbox';

export { Checkbox };
