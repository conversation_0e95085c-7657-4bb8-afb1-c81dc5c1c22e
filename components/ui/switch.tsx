'use client';

import * as React from 'react';
import { Switch as RadixSwitch } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Switch = React.forwardRef<
  React.ElementRef<typeof RadixSwitch>,
  React.ComponentPropsWithoutRef<typeof RadixSwitch>
>(({ className, ...props }, ref) => (
  <RadixSwitch
    className={cn('', className)}
    {...props}
    ref={ref}
  />
));
Switch.displayName = 'Switch';

export { Switch };
