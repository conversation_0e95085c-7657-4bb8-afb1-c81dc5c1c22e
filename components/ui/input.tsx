import * as React from 'react';
import { TextField } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

export interface InputProps extends React.ComponentProps<typeof TextField.Root> {
  placeholder?: string;
}

const Input = React.forwardRef<
  React.ElementRef<typeof TextField.Root>,
  InputProps
>(({ className, placeholder, ...props }, ref) => {
  return (
    <TextField.Root
      className={cn('', className)}
      placeholder={placeholder}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = 'Input';

export { Input };
