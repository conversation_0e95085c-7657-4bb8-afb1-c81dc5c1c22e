'use client';

import * as React from 'react';
import { Slider as RadixSlider } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Slider = React.forwardRef<
  React.ElementRef<typeof RadixSlider>,
  React.ComponentPropsWithoutRef<typeof RadixSlider>
>(({ className, ...props }, ref) => (
  <RadixSlider
    ref={ref}
    className={cn('', className)}
    {...props}
  />
));
Slider.displayName = 'Slider';

export { Slider };
