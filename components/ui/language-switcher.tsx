'use client'

import { useState, useTransition } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Globe } from 'lucide-react'
import { localeNames, localeNamesAbbreviated, type Locale } from '@/lib/i18n'

interface LanguageSwitcherProps {
  currentLocale: Locale
}

export function LanguageSwitcher({ currentLocale }: LanguageSwitcherProps) {
  const [isPending, startTransition] = useTransition()
  const [locale, setLocale] = useState(currentLocale)

  const handleLocaleChange = (newLocale: string) => {
    setLocale(newLocale as Locale)
    
    startTransition(() => {
      // Set cookie and reload the page
      document.cookie = `locale=${newLocale};path=/;max-age=${60 * 60 * 24 * 365}`
      window.location.reload()
    })
  }

  return (
    <Select value={locale} onValueChange={handleLocaleChange} disabled={isPending}>
      <SelectTrigger className="w-[60px] px-2 h-8 text-xs">
        <SelectValue>
          {localeNamesAbbreviated[locale]}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {(Object.entries(localeNamesAbbreviated) as [Locale, string][]).map(([code, abbrev]) => (
          <SelectItem key={code} value={code}>
            <span className="text-xs">{abbrev}</span>
            <span className="ml-2 text-xs text-muted-foreground">{localeNames[code]}</span>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}