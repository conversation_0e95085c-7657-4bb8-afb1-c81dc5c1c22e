'use client';

import * as React from 'react';
import { HoverCard as RadixHoverCard } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const HoverCard = RadixHoverCard.Root;

const HoverCardTrigger = RadixHoverCard.Trigger;

const HoverCardContent = React.forwardRef<
  React.ElementRef<typeof RadixHoverCard.Content>,
  React.ComponentPropsWithoutRef<typeof RadixHoverCard.Content>
>(({ className, ...props }, ref) => (
  <RadixHoverCard.Content
    ref={ref}
    className={cn('', className)}
    {...props}
  />
));
HoverCardContent.displayName = 'HoverCardContent';

export { HoverCard, HoverCardTrigger, HoverCardContent };
