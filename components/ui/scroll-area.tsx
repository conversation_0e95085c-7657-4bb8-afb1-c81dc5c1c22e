'use client';

import * as React from 'react';
import { ScrollArea as RadixScrollArea } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const ScrollArea = React.forwardRef<
  React.ElementRef<typeof RadixScrollArea>,
  React.ComponentPropsWithoutRef<typeof RadixScrollArea>
>(({ className, ...props }, ref) => (
  <RadixScrollArea
    ref={ref}
    className={cn('', className)}
    {...props}
  />
));
ScrollArea.displayName = 'ScrollArea';

// Legacy component for backward compatibility
const ScrollBar = ({ className, ...props }: { className?: string; [key: string]: any }) => (
  <div className={cn('', className)} {...props} />
);

export { ScrollArea, ScrollBar };
