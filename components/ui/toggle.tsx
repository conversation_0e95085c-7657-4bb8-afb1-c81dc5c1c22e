'use client';

import * as React from 'react';
import { Button } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

// Toggle component using Radix Themes Button with toggle state
interface ToggleProps extends React.ComponentProps<typeof Button> {
  pressed?: boolean;
  onPressedChange?: (pressed: boolean) => void;
}

const Toggle = React.forwardRef<
  React.ElementRef<typeof Button>,
  ToggleProps
>(({ className, pressed, onPressedChange, onClick, ...props }, ref) => (
  <Button
    ref={ref}
    variant={pressed ? "solid" : "soft"}
    className={cn('', className)}
    onClick={(e) => {
      onPressedChange?.(!pressed);
      onClick?.(e);
    }}
    {...props}
  />
));

Toggle.displayName = 'Toggle';

export { Toggle };

// Legacy export for backward compatibility
export const toggleVariants = () => '';
