'use client';

import * as React from 'react';
import { Text } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const Label = React.forwardRef<
  HTMLLabelElement,
  React.LabelHTMLAttributes<HTMLLabelElement>
>(({ className, children, ...props }, ref) => (
  <Text asChild>
    <label
      ref={ref}
      className={cn('', className)}
      {...props}
    >
      {children}
    </label>
  </Text>
));
Label.displayName = 'Label';

export { Label };
