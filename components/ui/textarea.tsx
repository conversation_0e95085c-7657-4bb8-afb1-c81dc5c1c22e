import * as React from 'react';
import { TextArea } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

export interface TextareaProps extends React.ComponentProps<typeof TextArea> {}

const Textarea = React.forwardRef<
  React.ElementRef<typeof TextArea>,
  TextareaProps
>(({ className, ...props }, ref) => {
  return (
    <TextArea
      className={cn('', className)}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = 'Textarea';

export { Textarea };
