'use client';

import * as React from 'react';
import { DropdownMenu as RadixDropdownMenu } from '@radix-ui/themes';
import { Check, ChevronRight, Circle } from 'lucide-react';

import { cn } from '@/lib/utils';

const DropdownMenu = RadixDropdownMenu.Root;

const DropdownMenuTrigger = RadixDropdownMenu.Trigger;

const DropdownMenuGroup = ({ children }: { children: React.ReactNode }) => children;

const DropdownMenuPortal = ({ children }: { children: React.ReactNode }) => children;

const DropdownMenuSub = RadixDropdownMenu.Sub;

const DropdownMenuRadioGroup = ({ children }: { children: React.ReactNode }) => children;

const DropdownMenuSubTrigger = RadixDropdownMenu.SubTrigger;

const DropdownMenuSubContent = RadixDropdownMenu.SubContent;

const DropdownMenuContent = RadixDropdownMenu.Content;

const DropdownMenuItem = RadixDropdownMenu.Item;

// Simplified implementations for backward compatibility
const DropdownMenuCheckboxItem = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
  <DropdownMenuItem {...props}>
    {children}
  </DropdownMenuItem>
);

const DropdownMenuRadioItem = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
  <DropdownMenuItem {...props}>
    {children}
  </DropdownMenuItem>
);

const DropdownMenuLabel = ({ children, className, ...props }: { children: React.ReactNode; className?: string; [key: string]: any }) => (
  <div className={cn('px-2 py-1.5 text-sm font-semibold', className)} {...props}>
    {children}
  </div>
);

const DropdownMenuSeparator = ({ className, ...props }: { className?: string; [key: string]: any }) => (
  <div className={cn('-mx-1 my-1 h-px bg-muted', className)} {...props} />
);

const DropdownMenuShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}
      {...props}
    />
  );
};
DropdownMenuShortcut.displayName = 'DropdownMenuShortcut';

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
};
