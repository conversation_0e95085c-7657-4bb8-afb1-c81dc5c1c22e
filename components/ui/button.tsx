import * as React from 'react';
import { Button as RadixButton } from '@radix-ui/themes';
import { Slot } from '@radix-ui/react-slot';

export interface ButtonProps extends React.ComponentProps<typeof RadixButton> {
  asChild?: boolean;
}

const Button = React.forwardRef<
  React.ElementRef<typeof RadixButton>,
  ButtonProps
>(({ asChild = false, ...props }, ref) => {
  if (asChild) {
    return (
      <Slot>
        <RadixButton {...props} ref={ref} />
      </Slot>
    );
  }
  return <RadixButton {...props} ref={ref} />;
});

Button.displayName = 'Button';

export { Button };

// Legacy export for backward compatibility
export const buttonVariants = () => '';
