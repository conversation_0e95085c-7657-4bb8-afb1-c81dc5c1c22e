import * as React from 'react';
import { Callout } from '@radix-ui/themes';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const alertVariants = cva('', {
  variants: {
    variant: {
      default: '',
      destructive: '',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

export interface AlertProps extends Omit<React.ComponentPropsWithoutRef<typeof Callout.Root>, 'color' | 'variant'> {
  variant?: 'default' | 'destructive';
}

const Alert = React.forwardRef<
  React.ElementRef<typeof Callout.Root>,
  AlertProps
>(({ className, variant = 'default', children, ...props }, ref) => {
  const color = variant === 'destructive' ? 'red' : 'blue';

  return (
    <Callout.Root
      ref={ref}
      color={color}
      className={cn('', className)}
      {...props}
    >
      {children}
    </Callout.Root>
  );
});
Alert.displayName = 'Alert';

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
  <Callout.Text>
    <div
      ref={ref}
      className={cn('mb-1 font-medium leading-none tracking-tight', className)}
      {...props}
    >
      {children}
    </div>
  </Callout.Text>
));
AlertTitle.displayName = 'AlertTitle';

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => (
  <Callout.Text>
    <div
      ref={ref}
      className={cn('text-sm [&_p]:leading-relaxed', className)}
      {...props}
    >
      {children}
    </div>
  </Callout.Text>
));
AlertDescription.displayName = 'AlertDescription';

export { Alert, AlertTitle, AlertDescription };
