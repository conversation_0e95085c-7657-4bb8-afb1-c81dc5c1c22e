'use client';

import * as React from 'react';
import { Tooltip as RadixTooltip } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const TooltipProvider = ({ children }: { children: React.ReactNode }) => children;

const Tooltip = RadixTooltip;

const TooltipTrigger = ({ children }: { children: React.ReactNode }) => children;

const TooltipContent = ({ children, className, ...props }: {
  children: React.ReactNode;
  className?: string;
  [key: string]: any
}) => (
  <span className={cn('', className)} {...props}>
    {children}
  </span>
);

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };
