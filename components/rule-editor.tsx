"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";
import { CodeEditor } from "@/components/ui/code-editor";
import { Rule, RulePayload, IDEType, VisibilityType, RuleSection } from "@/lib/store";
import { RuleSectionsList } from "./rule-sections-list";
import { parseRuleContent, formatRuleContent } from "@/lib/rule-sections";

interface RuleEditorProps {
  rule?: Rule;
  onSave: (rule: Partial<RulePayload>) => void;
  onCancel: () => void;
}

export function RuleEditor({ rule, onSave, onCancel }: RuleEditorProps) {
  const [title, setTitle] = useState(rule?.title || "");
  const [description, setDescription] = useState(rule?.description || "");
  const [sections, setSections] = useState<RuleSection[]>([]);
  const [ideType, setIdeType] = useState(rule?.ideType || "GENERAL");
  const [visibility, setVisibility] = useState(rule?.visibility || "PRIVATE");
  const [tags, setTags] = useState<string[]>(
    rule?.tags.map(t => t.tag.name) || []
  );
  const [newTag, setNewTag] = useState("");

  // Initialize sections from rule content
  useEffect(() => {
    if (rule?.content) {
      const parsedSections = parseRuleContent(rule.content);
      setSections(parsedSections);
    } else {
      // Create a default empty section for new rules
      setSections([{
        id: `section_${Date.now()}`,
        title: 'Section 1',
        content: '',
      }]);
    }
  }, [rule?.content]);

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = () => {
    // Format sections back into content string
    const formattedContent = formatRuleContent(sections);

    onSave({
      title,
      description,
      content: formattedContent,
      ideType: ideType as any,
      visibility: visibility as any,
      tags,
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="title">Rule Title</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter rule title..."
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe what this rule does..."
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="ide-type">IDE Type</Label>
            <Select value={ideType} onValueChange={(value) => setIdeType(value as IDEType)}>
              <SelectTrigger>
                <SelectValue placeholder="Select IDE type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GENERAL">General</SelectItem>
                <SelectItem value="CURSOR">Cursor</SelectItem>
                <SelectItem value="AUGMENT">Augment Code</SelectItem>
                <SelectItem value="WINDSURF">Windsurf</SelectItem>
                <SelectItem value="CLAUDE">Claude</SelectItem>
                <SelectItem value="GITHUB_COPILOT">GitHub Copilot</SelectItem>
                <SelectItem value="GEMINI">Gemini</SelectItem>
                <SelectItem value="OPENAI_CODEX">OpenAI Codex</SelectItem>
                <SelectItem value="CLINE">Cline</SelectItem>
                <SelectItem value="JUNIE">Junie</SelectItem>
                <SelectItem value="TRAE">Trae</SelectItem>
                <SelectItem value="LINGMA">Lingma</SelectItem>
                <SelectItem value="KIRO">Kiro</SelectItem>
                <SelectItem value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="visibility">Visibility</Label>
            <Select value={visibility} onValueChange={(value) => setVisibility(value as VisibilityType)}>
              <SelectTrigger>
                <SelectValue placeholder="Select visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PRIVATE">Private</SelectItem>
                <SelectItem value="PUBLIC">Public</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label>Tags</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {tags.map((tag) => (
              <Badge key={tag} variant="soft" className="gap-1">
                {tag}
                <Button
                  variant="ghost"
                  size="1"
                  className="h-auto p-0 w-4 h-4"
                  onClick={() => handleRemoveTag(tag)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
            />
            <Button onClick={handleAddTag} size="1">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <RuleSectionsList
          sections={sections}
          onSectionsChange={setSections}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          {rule ? "Update Rule" : "Create Rule"}
        </Button>
      </div>
    </div>
  );
}