'use client'

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { useLingui } from '@lingui/react'

export function HeroSection() {
  const { i18n } = useLingui()
  
  return (
    <section className="relative overflow-hidden py-24 md:py-32">
      <div className="container relative z-10 mx-auto px-4">
        <div className="mx-auto max-w-5xl text-center">
          <h1 className="mb-6 text-4xl font-bold tracking-tight md:text-6xl">
            {i18n._("hero.title")}
          </h1>
          <p className="mb-8 text-lg text-muted-foreground md:text-xl">
            {i18n._("hero.subtitle")}
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Button size="3" asChild>
              <Link href="/auth/signup">
                {i18n._("hero.getStarted")}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="3" variant="outline" asChild>
              <Link href="/templates">
                {i18n._("hero.browseTemplates")}
              </Link>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-[50%] top-0 h-[800px] w-[800px] -translate-x-[50%] rounded-full bg-primary/20 blur-[100px]" />
      </div>
    </section>
  )
}