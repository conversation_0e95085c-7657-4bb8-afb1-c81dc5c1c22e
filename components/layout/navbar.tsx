"use client";

import Link from "next/link";
import { useState } from "react";
import { useTheme } from "next-themes";
import { Moon, Sun, Code, User, LogOut, Settings, Github } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { useSession, signOut } from "@/lib/auth-client";
import { LanguageSwitcher } from "@/components/ui/language-switcher";
import { IDEPreferenceManager } from "@/components/ide-preferences/ide-preference-manager";
import { type Locale } from "@/lib/i18n";
// import { useLingui } from '@lingui/react';

interface NavbarProps {
  locale: Locale;
}

export function Navbar({ locale }: NavbarProps) {
  const { theme, setTheme } = useTheme();
  // Temporarily disabled for build
  // const { data: session } = useSession();
  const session: any = null; // Temporarily disabled
  const [showIDEPreferences, setShowIDEPreferences] = useState(false);
  // const { i18n } = useLingui();

  const handleSignOut = async () => {
    // Temporarily disabled for build
    // await signOut();
  };

  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link href="/" className="flex items-center gap-2">
            <Code className="h-6 w-6 text-primary" />
            <span className="font-bold text-xl">OnlyRules</span>
          </Link>
          
          <nav className="hidden md:flex items-center gap-6">
            <Link
              href="/dashboard"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Dashboard
            </Link>
            <Link
              href="/templates"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Templates
            </Link>
            <Link
              href="/ides"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              IDEs
            </Link>
            <Link
              href="/tutorials"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Tutorials
            </Link>
            <Link
              href="/shared"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              Community
            </Link>
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <LanguageSwitcher currentLocale={locale} />
          
          <Button variant="ghost" size="2" asChild>
            <Link
              href="https://github.com/ranglang/onlyrules"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2"
            >
              <Github className="h-4 w-4" />
              <span className="hidden sm:inline">GitHub</span>
            </Link>
          </Button>

          <Button
            variant="ghost"
            size="2"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>

          {session?.user ? (
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar 
                    className="h-8 w-8"
                    src={session.user.image || ""}
                    fallback={session.user.name?.charAt(0) || session.user.email?.charAt(0) || "U"}
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    {session.user.name && (
                      <p className="font-medium">{session.user.name}</p>
                    )}
                    {session.user.email && (
                      <p className="w-[200px] truncate text-sm text-muted-foreground">
                        {session.user.email}
                      </p>
                    )}
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">
                    <User className="mr-2 h-4 w-4" />
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowIDEPreferences(true)}>
                  <Code className="mr-2 h-4 w-4" />
                  IDE Preferences
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="2">
                <Link href="/auth/signin">Sign In</Link>
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* IDE Preferences Dialog */}
      <IDEPreferenceManager
        open={showIDEPreferences}
        onOpenChange={setShowIDEPreferences}
      />
    </nav>
  );
}