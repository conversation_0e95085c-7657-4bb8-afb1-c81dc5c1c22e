import Link from "next/link";
import { StaticNavbar } from "@/components/layout/static-navbar";

// Allow dynamic rendering
export const dynamic = 'auto';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      <StaticNavbar />
      <div className="container mx-auto px-4 py-16 flex-1">
        <div className="flex flex-col items-center gap-6 text-center">
          <h1 className="text-6xl font-bold">
            OnlyRules
          </h1>
          <p className="text-xl text-muted-foreground">
            AI Prompt Management Platform
          </p>
          <div className="flex gap-4 mt-4">
            <Link
              href="/auth/signin"
              className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
            >
              Get Started
            </Link>
            <Link
              href="/templates"
              className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
            >
              Browse Templates
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
