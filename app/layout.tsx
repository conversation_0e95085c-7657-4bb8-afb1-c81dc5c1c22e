import './globals.css';
import type { Metadata } from 'next';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { JotaiProvider } from '@/components/providers/jotai-provider';
import { LinguiProvider } from '@/components/providers/LinguiProvider';
import { NavbarWrapper } from '@/components/layout/navbar-wrapper';
import { Toaster } from '@/components/ui/sonner';
import { getLocale } from '@/lib/locale';
// import { Flex, Container, Text, Link as RadixLink } from '@radix-ui/themes';

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  title: 'OnlyRules - AI Prompt Management Platform',
  description: 'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
  keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, <PERSON><PERSON><PERSON> Codex, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tencent Cloud CodeBuddy',
  authors: [{ name: 'OnlyRules Team' }],
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Use default locale during static generation
  let locale: 'en' | 'zh-CN' | 'zh-HK' = 'en';

  // Only try to get locale on the client side or when headers are available
  if (typeof window === 'undefined') {
    try {
      // Check if we're in a server environment with headers available
      const { headers } = require('next/headers');
      headers(); // This will throw if not available
      locale = getLocale();
    } catch (error) {
      // During static generation or when headers are not available, use default locale
      locale = 'en';
    }
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        {/* Google tag (gtag.js) */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-CYBBJ5J4SH"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-CYBBJ5J4SH');
            `,
          }}
        />
      </head>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <JotaiProvider>
            <div className="min-h-screen bg-background flex flex-col">
              <NavbarWrapper />
              <main className="flex-1">
                {children}
              </main>
              <footer className="border-t border-border pt-6 pb-6">
                <div className="container mx-auto">
                  <div className="flex flex-col md:flex-row items-center justify-between gap-4 min-h-16">
                    <div className="flex flex-col md:flex-row items-center gap-4">
                      <p className="text-sm text-center text-muted-foreground">
                        Built with ❤️ for the AI coding community.
                      </p>
                    </div>
                    <div className="flex flex-col md:flex-row items-center gap-2">
                      <p className="text-sm text-muted-foreground">Links:</p>
                      <a
                        href="https://toolsdk.ai/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary hover:underline"
                      >
                        ToolSDK.ai
                      </a>
                    </div>
                  </div>
                </div>
              </footer>
            </div>
            <Toaster />
          </JotaiProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}